from fastapi import FastAPI
from BusinessLogicLayer.api.HealthApi import health_router
from DataAccessLayer.DbConnection import DbConnectionModel
from fastapi.responses import JSONResponse

# Initialize FastAPI app
app = FastAPI(
    title="Hadith IQ Server - Minimal",
    description="Minimal API for Hadith IQ Project",
    version="1.0.0",
)

# Test database connection on startup
@app.on_event("startup")
async def startup_event():
    try:
        db = DbConnectionModel()
        db.testConnection()
        print("Database connection successful!")
    except Exception as e:
        print(f"Database connection failed: {e}")

# Include health router
app.include_router(health_router())

@app.get("/")
async def root():
    return JSONResponse(content={"message": "Hadith IQ Server is running!"}, status_code=200)

@app.get("/db-test")
async def test_database():
    try:
        db = DbConnectionModel()
        db.testConnection()
        return JSONResponse(content={"status": "Database connection successful"}, status_code=200)
    except Exception as e:
        return JSONResponse(content={"status": "Database connection failed", "error": str(e)}, status_code=500)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)
