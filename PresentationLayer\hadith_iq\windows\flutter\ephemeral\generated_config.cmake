# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\Desktop\\flutter_windows_3.32.6-stable\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\Desktop\\flutter_windows_3.32.6-stable\\flutter"
  "PROJECT_DIR=D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\Desktop\\flutter_windows_3.32.6-stable\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq"
  "FLUTTER_TARGET=D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNg==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049MDc3YjRhNGNlMQ==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049NzJmMmIxOGJiMA==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\fyp\\Hadith-IQ\\PresentationLayer\\hadith_iq\\.dart_tool\\package_config.json"
)
